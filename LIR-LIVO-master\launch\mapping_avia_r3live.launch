<launch>

	<arg name="rviz" default="true" />

	<rosparam command="load" file="$(find fast_livo)/config/R3live_CONFIG.yaml" />

	<node pkg="fast_livo" type="fastlivo_mapping" name="laserMapping" output="screen" >
		<rosparam file="$(find fast_livo)/config/camera_r3live.yaml" />
	</node>
	
	<group if="$(arg rviz)">
		<node launch-prefix="nice" pkg="rviz" type="rviz" name="rviz" args="-d $(find fast_livo)/rviz_cfg/loam_livox.rviz" />
	</group>
	<!-- <node pkg="image_transport" type="republish" name="republish" args="compressed in:=/left_camera/image raw out:=/left_camera/image" output="screen" respawn="true"/> -->

launch-prefix="gdb -ex run --args"
</launch> 	
